"""
Test RTDoseModule (M - Mandatory) functionality.

RTDoseModule implements DICOM PS3.3 C.8.8.3 RT Dose Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import RTDoseModule
from pyrt_dicom.enums import DoseUnits, DoseType, DoseSummationType, SpatialTransformOfDose, TissueHeterogeneityCorrection
from pyrt_dicom.enums import PhotometricInterpretation, PixelRepresentation
from pyrt_dicom.validators import ValidationResult


class TestRTDoseModule:
    """Test RTDoseModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        dataset = dose.to_dataset()
        assert dataset.DoseUnits == "GY"
        assert dataset.DoseType == "PHYSICAL"
        assert dataset.DoseSummationType == "PLAN"
    
    def test_from_required_elements_string_values(self):
        """Test creation with string values instead of enums."""
        dose = RTDoseModule.from_required_elements(
            dose_units="RELATIVE",
            dose_type="EFFECTIVE", 
            dose_summation_type="MULTI_PLAN"
        )
        
        dataset = dose.to_dataset()
        assert dataset.DoseUnits == "RELATIVE"
        assert dataset.DoseType == "EFFECTIVE"
        assert dataset.DoseSummationType == "MULTI_PLAN"
    
    def test_dose_units_validation(self):
        """Test dose units enum validation."""
        for units in [DoseUnits.GY, DoseUnits.RELATIVE]:
            dose = RTDoseModule.from_required_elements(
                dose_units=units,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=DoseSummationType.PLAN
            )
            dataset = dose.to_dataset()
            assert dataset.DoseUnits == units.value
    
    def test_dose_type_validation(self):
        """Test dose type enum validation."""
        for dose_type in [DoseType.PHYSICAL, DoseType.EFFECTIVE, DoseType.ERROR]:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=dose_type,
                dose_summation_type=DoseSummationType.PLAN
            )
            dataset = dose.to_dataset()
            assert dataset.DoseType == dose_type.value
    
    def test_dose_summation_type_validation(self):
        """Test dose summation type enum validation."""
        for summation_type in [DoseSummationType.PLAN, DoseSummationType.MULTI_PLAN, 
                              DoseSummationType.FRACTION, DoseSummationType.RECORD]:
            dose = RTDoseModule.from_required_elements(
                dose_units=DoseUnits.GY,
                dose_type=DoseType.PHYSICAL,
                dose_summation_type=summation_type
            )
            dataset = dose.to_dataset()
            assert dataset.DoseSummationType == summation_type.value
    
    def test_with_pixel_data_elements_valid(self):
        """Test adding valid pixel data elements."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=PixelRepresentation.UNSIGNED,
            dose_grid_scaling=0.001
        )
        
        dataset = dose.to_dataset()
        assert dataset.SamplesPerPixel == 1
        assert dataset.PhotometricInterpretation == "MONOCHROME2"
        assert dataset.BitsAllocated == 16
        assert dataset.BitsStored == 16
        assert dataset.HighBit == 15
        assert dataset.PixelRepresentation == 0
        assert dataset.DoseGridScaling == 0.001
    
    def test_pixel_data_elements_32bit(self):
        """Test 32-bit pixel data configuration."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=32,
            bits_stored=32,
            high_bit=31,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        
        dataset = dose.to_dataset()
        assert dataset.BitsAllocated == 32
        assert dataset.BitsStored == 32
        assert dataset.HighBit == 31
    
    def test_pixel_data_elements_with_grid_frame_offset(self):
        """Test pixel data elements with grid frame offset vector."""
        grid_offsets = [0.0, 2.0, 4.0, 6.0, 8.0]
        
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation=PhotometricInterpretation.MONOCHROME2,
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0,
            dose_grid_scaling=0.001,
            grid_frame_offset_vector=grid_offsets
        )
        
        dataset = dose.to_dataset()
        assert dataset.GridFrameOffsetVector == grid_offsets
    
    def test_pixel_data_constraints_samples_per_pixel(self):
        """Test DICOM constraint: samples per pixel must be 1 for RT Dose."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        with pytest.raises(ValueError, match="Samples per Pixel must be 1"):
            dose.with_pixel_data_elements(
                samples_per_pixel=3,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
    
    def test_pixel_data_constraints_photometric_interpretation(self):
        """Test DICOM constraint: photometric interpretation must be MONOCHROME2."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        with pytest.raises(ValueError, match="Photometric Interpretation must be MONOCHROME2"):
            dose.with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="RGB",
                bits_allocated=16,
                bits_stored=16,
                high_bit=15,
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
    
    def test_pixel_data_constraints_bits_allocated(self):
        """Test DICOM constraint: bits allocated must be 16 or 32."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        with pytest.raises(ValueError, match="Bits Allocated must be 16 or 32"):
            dose.with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=8,
                bits_stored=8,
                high_bit=7,
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
    
    def test_pixel_data_constraints_bits_stored_equals_allocated(self):
        """Test DICOM constraint: bits stored must equal bits allocated."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        with pytest.raises(ValueError, match="Bits Stored must equal Bits Allocated"):
            dose.with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=16,
                bits_stored=14,
                high_bit=15,
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
    
    def test_pixel_data_constraints_high_bit(self):
        """Test DICOM constraint: high bit must be one less than bits stored."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        with pytest.raises(ValueError, match="High Bit must be one less than Bits Stored"):
            dose.with_pixel_data_elements(
                samples_per_pixel=1,
                photometric_interpretation="MONOCHROME2",
                bits_allocated=16,
                bits_stored=16,
                high_bit=16,  # Should be 15
                pixel_representation=0,
                dose_grid_scaling=0.001
            )
    
    def test_pixel_representation_error_dose_type(self):
        """Test pixel representation must be signed for ERROR dose type."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.ERROR,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        # Should work with signed representation
        dose.with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=1,  # Signed
            dose_grid_scaling=0.001
        )
        
        dataset = dose.to_dataset()
        assert dataset.PixelRepresentation == 1
    
    def test_pixel_representation_non_error_dose_type(self):
        """Test pixel representation must be unsigned for non-ERROR dose types."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        dose.with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0,  # Unsigned
            dose_grid_scaling=0.001
        )
        
        dataset = dose.to_dataset()
        assert dataset.PixelRepresentation == 0
    
    def test_with_referenced_rt_plan(self):
        """Test adding referenced RT plan."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_referenced_rt_plan(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.5",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9"
        )
        
        dataset = dose.to_dataset()
        assert hasattr(dataset, 'ReferencedRTPlanSequence')
        assert len(dataset.ReferencedRTPlanSequence) == 1
        assert dataset.ReferencedRTPlanSequence[0].ReferencedSOPClassUID == "1.2.840.10008.5.1.4.1.1.481.5"
        assert dataset.ReferencedRTPlanSequence[0].ReferencedSOPInstanceUID == "1.2.3.4.5.6.7.8.9"
    
    def test_with_referenced_spatial_registration(self):
        """Test adding referenced spatial registration."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_referenced_spatial_registration(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.66.1",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.10"
        )
        
        dataset = dose.to_dataset()
        assert hasattr(dataset, 'ReferencedSpatialRegistrationSequence')
        assert len(dataset.ReferencedSpatialRegistrationSequence) == 1
        assert dataset.ReferencedSpatialRegistrationSequence[0].ReferencedSOPClassUID == "1.2.840.10008.5.1.4.1.1.66.1"
    
    def test_with_referenced_treatment_record(self):
        """Test adding referenced treatment record."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.RECORD
        ).with_referenced_treatment_record(
            referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.4",
            referenced_sop_instance_uid="1.2.3.4.5.6.7.8.11"
        )
        
        dataset = dose.to_dataset()
        assert hasattr(dataset, 'ReferencedTreatmentRecordSequence')
        assert len(dataset.ReferencedTreatmentRecordSequence) == 1
        assert dataset.ReferencedTreatmentRecordSequence[0].ReferencedSOPClassUID == "1.2.840.10008.5.1.4.1.1.481.4"
    
    def test_with_plan_overview(self):
        """Test adding plan overview."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN_OVERVIEW
        ).with_plan_overview(
            plan_overview_index=1,
            rt_plan_label="Test Plan",
            number_of_fractions_included=30,
            treatment_site="PROSTATE"
        )
        
        dataset = dose.to_dataset()
        assert hasattr(dataset, 'PlanOverviewSequence')
        assert len(dataset.PlanOverviewSequence) == 1
        assert dataset.PlanOverviewSequence[0].PlanOverviewIndex == 1
        assert dataset.PlanOverviewSequence[0].RTPlanLabel == "Test Plan"
        assert dataset.PlanOverviewSequence[0].NumberOfFractionsIncluded == 30
        assert dataset.PlanOverviewSequence[0].TreatmentSite == "PROSTATE"
    
    def test_with_optional_elements(self):
        """Test adding optional dose-specific elements."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_optional_elements(
            content_date="20240101",
            content_time="120000",
            spatial_transform_of_dose=SpatialTransformOfDose.RIGID,
            instance_number=1,
            entity_long_label="Primary Dose Distribution",
            dose_comment="Test dose distribution",
            normalization_point=[0.0, 0.0, 0.0],
            tissue_heterogeneity_correction=TissueHeterogeneityCorrection.IMAGE
        )
        
        dataset = dose.to_dataset()
        assert dataset.ContentDate == "20240101"
        assert dataset.ContentTime == "120000"
        assert dataset.SpatialTransformOfDose == "RIGID"
        assert dataset.InstanceNumber == "1"
        assert dataset.EntityLongLabel == "Primary Dose Distribution"
        assert dataset.DoseComment == "Test dose distribution"
        assert dataset.NormalizationPoint == [0.0, 0.0, 0.0]
        assert dataset.TissueHeterogeneityCorrection == "IMAGE"
    
    def test_normalization_point_validation(self):
        """Test normalization point must have exactly 3 coordinates."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        with pytest.raises(ValueError, match="Normalization Point must have exactly 3 coordinates"):
            dose.with_optional_elements(normalization_point=[0.0, 0.0])
    
    def test_create_recommended_isodose_level_item(self):
        """Test creating recommended isodose level sequence items."""
        item = RTDoseModule.create_recommended_isodose_level_item(
            dose_value=20.0,
            recommended_display_cielab_value=[255, 128, 0]
        )
        
        assert isinstance(item, Dataset)
        assert item.DoseValue == 20.0
        assert item.RecommendedDisplayCIELabValue == [255, 128, 0]
    
    def test_create_recommended_isodose_level_item_validation(self):
        """Test validation for recommended isodose level items."""
        with pytest.raises(ValueError, match="Recommended Display CIELab Value must have exactly 3 values"):
            RTDoseModule.create_recommended_isodose_level_item(
                dose_value=20.0,
                recommended_display_cielab_value=[255, 128]  # Missing third value
            )
    
    def test_create_referenced_fraction_group_item(self):
        """Test creating referenced fraction group sequence items."""
        item = RTDoseModule.create_referenced_fraction_group_item(
            referenced_fraction_group_number=1
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedFractionGroupNumber == 1
    
    def test_create_referenced_beam_item(self):
        """Test creating referenced beam sequence items."""
        item = RTDoseModule.create_referenced_beam_item(
            referenced_beam_number=1
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedBeamNumber == 1
    
    def test_create_referenced_control_point_item(self):
        """Test creating referenced control point sequence items."""
        item = RTDoseModule.create_referenced_control_point_item(
            referenced_start_control_point_index=0,
            referenced_stop_control_point_index=1
        )
        
        assert isinstance(item, Dataset)
        assert item.ReferencedStartControlPointIndex == 0
        assert item.ReferencedStopControlPointIndex == 1
    
    def test_create_referenced_control_point_item_validation(self):
        """Test validation for referenced control point items."""
        with pytest.raises(ValueError, match="Referenced Stop Control Point Index must be immediately following"):
            RTDoseModule.create_referenced_control_point_item(
                referenced_start_control_point_index=0,
                referenced_stop_control_point_index=2  # Should be 1
            )
    
    def test_create_prescription_overview_item(self):
        """Test creating prescription overview sequence items."""
        item = RTDoseModule.create_prescription_overview_item(
            total_prescription_dose=70.0,
            entity_long_label="PTV",
            referenced_roi_number=1
        )
        
        assert isinstance(item, Dataset)
        assert item.TotalPrescriptionDose == 70.0
        assert item.EntityLongLabel == "PTV"
        assert item.ReferencedROINumber == 1
    
    def test_has_pixel_data_property(self):
        """Test has_pixel_data property."""
        # Without pixel data
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        assert not dose.has_pixel_data
        
        # With pixel data
        dose.with_pixel_data_elements(
            samples_per_pixel=1,
            photometric_interpretation="MONOCHROME2",
            bits_allocated=16,
            bits_stored=16,
            high_bit=15,
            pixel_representation=0,
            dose_grid_scaling=0.001
        )
        assert dose.has_pixel_data
    
    def test_conditional_properties(self):
        """Test conditional requirement properties."""
        # RT Plan reference required for PLAN summation type
        dose_plan = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        assert dose_plan.requires_rt_plan_reference
        assert not dose_plan.requires_treatment_record_reference
        assert not dose_plan.requires_plan_overview
        
        # Treatment record required for RECORD summation type
        dose_record = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.RECORD
        )
        assert dose_record.requires_treatment_record_reference
        assert not dose_record.requires_rt_plan_reference
        
        # Plan overview required for PLAN_OVERVIEW summation type
        dose_overview = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN_OVERVIEW
        )
        assert dose_overview.requires_plan_overview
    
    def test_spatial_transform_properties(self):
        """Test spatial transform related properties."""
        # No spatial transform
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        assert not dose.has_spatial_transform
        assert not dose.requires_spatial_registration
        
        # With RIGID spatial transform
        dose.with_optional_elements(
            spatial_transform_of_dose=SpatialTransformOfDose.RIGID
        )
        assert dose.has_spatial_transform
        assert dose.requires_spatial_registration
    
    def test_is_configured_property(self):
        """Test is_configured property."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        assert dose.is_configured
    
    def test_to_dataset_method(self):
        """Test dataset generation method."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        ).with_optional_elements(
            content_date="20240101",
            dose_comment="Test dose"
        )
        
        dataset = dose.to_dataset()
        assert isinstance(dataset, Dataset)
        assert dataset.DoseUnits == "GY"
        assert dataset.DoseType == "PHYSICAL"
        assert dataset.DoseSummationType == "PLAN"
        assert dataset.ContentDate == "20240101"
        assert dataset.DoseComment == "Test dose"
    
    def test_validation_method_exists(self):
        """Test that validation method exists and returns correct type."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        assert hasattr(dose, 'validate')
        assert callable(dose.validate)
        
        validation_result = dose.validate()
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_module_properties(self):
        """Test module properties from BaseModule."""
        dose = RTDoseModule.from_required_elements(
            dose_units=DoseUnits.GY,
            dose_type=DoseType.PHYSICAL,
            dose_summation_type=DoseSummationType.PLAN
        )
        
        assert dose.module_name == "RTDoseModule"
        assert dose.has_data
        assert dose.get_element_count() == 3  # Three required elements
        assert "RTDoseModule" in str(dose)
    
    def test_method_chaining(self):
        """Test that all builder methods support method chaining."""
        dose = (RTDoseModule.from_required_elements(
                    dose_units=DoseUnits.GY,
                    dose_type=DoseType.PHYSICAL,
                    dose_summation_type=DoseSummationType.PLAN)
               .with_pixel_data_elements(
                    samples_per_pixel=1,
                    photometric_interpretation="MONOCHROME2",
                    bits_allocated=16,
                    bits_stored=16,
                    high_bit=15,
                    pixel_representation=0,
                    dose_grid_scaling=0.001)
               .with_referenced_rt_plan(
                    referenced_sop_class_uid="1.2.840.10008.5.1.4.1.1.481.5",
                    referenced_sop_instance_uid="1.2.3.4.5.6.7.8.9")
               .with_optional_elements(
                    content_date="20240101",
                    dose_comment="Test"))
        
        dataset = dose.to_dataset()
        assert dataset.DoseUnits == "GY"
        assert dataset.BitsAllocated == 16
        assert hasattr(dataset, 'ReferencedRTPlanSequence')
        assert dataset.ContentDate == "20240101"